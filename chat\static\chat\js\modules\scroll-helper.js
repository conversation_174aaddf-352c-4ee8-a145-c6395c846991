/**
 * <PERSON><PERSON><PERSON><PERSON> scroll-helper.js - Funciones auxiliares para el scroll
 *
 * Este módulo proporciona funciones adicionales para asegurar que el scroll
 * funcione correctamente en todos los navegadores y situaciones.
 */

/**
 * Inicializa los helpers de scroll
 */
export function initializeScrollHelpers() {
  // Añadir evento de scroll para detectar cuando el usuario está al final
  window.addEventListener('scroll', handleScroll, { passive: true });

  // Añadir evento de resize para ajustar el scroll cuando cambia el tamaño de la ventana
  window.addEventListener('resize', handleResize, { passive: true });

  // Inicializar observador de mutaciones para detectar cambios en el DOM
  setupMutationObserver();

  // Inicializar scroll al cargar la página
  setTimeout(scrollToBottom, 500);

  // Inicializar botón de navegación de scroll
  initializeScrollNavButton();
}

/**
 * Maneja el evento de scroll
 */
function handleScroll() {
  // Guardar la posición de scroll actual
  const isAtBottom = isUserAtBottom();

  // Guardar en sessionStorage si el usuario está al final
  sessionStorage.setItem('isAtBottom', isAtBottom);
}

/**
 * Maneja el evento de resize
 */
function handleResize() {
  // Verificar si el usuario estaba al final antes del resize
  const wasAtBottom = sessionStorage.getItem('isAtBottom') === 'true';

  if (wasAtBottom) {
    // Si estaba al final, mantenerlo al final
    scrollToBottom();
  }
}

/**
 * Verifica si el usuario está cerca del final de la página
 * @returns {boolean} - True si el usuario está cerca del final
 */
function isUserAtBottom() {
  const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;
  const windowHeight = window.innerHeight;
  const documentHeight = Math.max(
    document.body.scrollHeight, document.documentElement.scrollHeight,
    document.body.offsetHeight, document.documentElement.offsetHeight,
    document.body.clientHeight, document.documentElement.clientHeight
  );

  // Consideramos que está al final si está a menos de 300px del final
  return documentHeight - scrollPosition - windowHeight < 300;
}

/**
 * Hace scroll al final de la página
 */
function scrollToBottom() {
  // Usar solo un método de scroll para mayor consistencia
  try {
    // Método: scroll del último mensaje (al inicio del mensaje en lugar del final)
    const lastMessage = document.querySelector('.chat-message:last-child');
    if (lastMessage) {
      lastMessage.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  } catch (error) {
    console.error('Error al hacer scroll:', error);
  }
}

/**
 * Configura un observador de mutaciones para detectar cambios en el DOM
 */
function setupMutationObserver() {
  // Crear observador de mutaciones
  const observer = new MutationObserver(() => {
    // Detectar cambios en el contenido para actualizar el botón de navegación
    const scrollNavButton = document.getElementById('scroll-nav-button');
    const scrollNavIcon = document.getElementById('scroll-nav-icon');

    if (scrollNavButton && scrollNavIcon) {
      setTimeout(() => {
        updateScrollNavButtonState(scrollNavButton, scrollNavIcon);
      }, 100);
    }
  });

  // Iniciar observación en el contenedor de mensajes
  const chatBoxContent = document.getElementById('chat-box-content');
  if (chatBoxContent) {
    observer.observe(chatBoxContent, {
      childList: true,
      subtree: true
    });
  }
}

/**
 * Inicializa el botón de navegación de scroll
 */
function initializeScrollNavButton() {
  const scrollNavButton = document.getElementById('scroll-nav-button');
  const scrollNavIcon = document.getElementById('scroll-nav-icon');

  if (!scrollNavButton || !scrollNavIcon) return;

  // Asegurarnos de que el botón sea visible
  scrollNavButton.style.display = 'flex';

  // Actualizar el estado del botón al hacer scroll
  window.addEventListener('scroll', () => {
    updateScrollNavButtonState(scrollNavButton, scrollNavIcon);
  });

  // Manejar el clic en el botón
  scrollNavButton.addEventListener('click', () => {
    const isAtTop = window.scrollY < 100;

    if (isAtTop) {
      // Si estamos en la parte superior, ir al final
      scrollToBottom();
      // Tooltip removed
      scrollNavIcon.classList.remove('bx-arrow-down');
      scrollNavIcon.classList.add('bx-arrow-up');
    } else {
      // Si estamos en cualquier otra parte, ir al inicio
      scrollToTop();
      // Tooltip removed
      scrollNavIcon.classList.remove('bx-arrow-up');
      scrollNavIcon.classList.add('bx-arrow-down');
    }
  });

  // Verificar inicialmente si hay suficiente contenido para mostrar el botón
  setTimeout(() => {
    updateScrollNavButtonState(scrollNavButton, scrollNavIcon);
  }, 1000);
}

/**
 * Actualiza el estado del botón de navegación de scroll
 * @param {HTMLElement} button - El botón de navegación
 * @param {HTMLElement} icon - El icono dentro del botón
 */
function updateScrollNavButtonState(button, icon) {
  const scrollPosition = window.scrollY || document.documentElement.scrollTop;

  // Asegurarnos de que el botón siempre sea visible
  button.style.display = 'flex';

  // Cambiar el icono y tooltip según la posición
  const isAtTop = scrollPosition < 100;

  if (isAtTop) {
    // Tooltip removed
    icon.classList.remove('bx-arrow-up');
    icon.classList.add('bx-arrow-down');
  } else {
    // Tooltip removed
    icon.classList.remove('bx-arrow-down');
    icon.classList.add('bx-arrow-up');
  }
}

/**
 * Hace scroll al inicio de la página
 */
function scrollToTop() {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  });
}