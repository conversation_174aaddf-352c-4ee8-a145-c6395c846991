/* Import variables from chat app */
@import url('/static/chat/css/variables.css');

/* Import base styles */
@import url('/static/chat/css/base.css');

/* Additional base styles */
html {
    scroll-behavior: smooth;
}

body {
    overflow-x: hidden;
    height: auto;
    display: block;
}

/* Container */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
}

h1 {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
}

h2 {
    font-size: 2.5rem;
    margin-bottom: 1.2rem;
}

h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

p {
    margin-bottom: 1rem;
}

.subtitle {
    font-size: 1.2rem;
    color: var(--text-dimmed);
    margin-bottom: 2rem;
}

.highlight {
    color: var(--accent-primary);
    position: relative;
    display: inline-block;
}

.highlight::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 30%;
    background-color: rgba(var(--accent-primary-rgb), 0.1);
    z-index: -1;
    border-radius: 4px;
}

/* Navbar */
.navbar {
    padding: 1.5rem 0;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    transition: var(--transition);
    background-color: transparent;
}

.navbar.scrolled {
    background-color: var(--bg-secondary);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 1rem 0;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.nav-btn {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
}

.nav-btn:hover {
    transform: translateY(-2px);
}

.nav-btn.primary {
    background-color: var(--accent-primary);
    color: var(--text-primary);
}

.welcome-user {
    color: var(--text-dimmed);
    margin-right: 1rem;
    font-size: var(--font-size-sm);
}

/* Hero Section */
.hero {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    padding: 0;
    background: linear-gradient(135deg, var(--bg-secondary), var(--bg-primary));
}

.hero-content {
    text-align: center;
    max-width: 800px;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.hero h1 {
    color: var(--text-primary);
    font-size: 4rem;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero .subtitle {
    color: var(--text-secondary);
    font-size: 1.5rem;
    margin-bottom: 3rem;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    background-color: var(--accent-primary);
    color: var(--text-primary);
    text-decoration: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 1.1rem;
    transition: var(--transition);
    box-shadow: 0 4px 15px rgba(var(--accent-primary-rgb), 0.3);
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(var(--accent-primary-rgb), 0.4);
}

.cta-button svg {
    transition: var(--transition);
}

.cta-button:hover svg {
    transform: translateX(5px);
}

.animated-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    background: 
        radial-gradient(circle at 20% 30%, rgba(var(--accent-primary-rgb), 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(var(--accent-secondary-rgb), 0.05) 0%, transparent 50%);
}

/* Features Section */
.features {
    padding: 6rem 0;
    background-color: var(--bg-primary);
    position: relative;
}

.features::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 10% 20%, rgba(74, 144, 226, 0.03) 0%, transparent 30%),
        radial-gradient(circle at 90% 80%, rgba(92, 156, 230, 0.03) 0%, transparent 30%);
    z-index: 1;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2.5rem;
    position: relative;
    z-index: 2;
}

.feature-card {
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    transition: var(--transition);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background-color: rgba(var(--accent-primary-rgb), 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.feature-icon svg {
    width: 30px;
    height: 30px;
    color: var(--accent-primary);
}

.feature-card h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.feature-card p {
    color: var(--text-dimmed);
    flex-grow: 1;
}

/* Section Header */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
    position: relative;
    z-index: 2;
}

.section-header h2 {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.section-header p {
    color: var(--text-dimmed);
    max-width: 600px;
    margin: 0 auto;
}

/* Tools Section */
.tools {
    padding: 6rem 0;
    background-color: var(--bg-secondary);
    position: relative;
}

.tools::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 80% 20%, rgba(var(--accent-secondary-rgb), 0.03) 0%, transparent 30%),
        radial-gradient(circle at 20% 80%, rgba(var(--accent-primary-rgb), 0.03) 0%, transparent 30%);
    z-index: 1;
}

.tools-showcase {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2.5rem;
    position: relative;
    z-index: 2;
}

.tool-card {
    background-color: var(--bg-tertiary);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    transition: var(--transition);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.tool-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.tool-image {
    height: 200px;
    background-color: var(--bg-quaternary);
    display: flex;
    align-items: center;
    justify-content: center;
}

.tool-icon {
    width: 80px;
    height: 80px;
    background-color: rgba(var(--accent-primary-rgb), 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tool-icon svg {
    width: 40px;
    height: 40px;
    color: var(--accent-primary);
}

.tool-content {
    padding: 2rem;
}

.tool-content h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.tool-content p {
    color: var(--text-dimmed);
    margin-bottom: 1.5rem;
}

/* Demo Section */
.demo {
    padding: 6rem 0;
    background-color: var(--bg-primary);
    position: relative;
}

.demo::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 30% 30%, rgba(var(--accent-primary-rgb), 0.03) 0%, transparent 30%),
        radial-gradient(circle at 70% 70%, rgba(var(--accent-secondary-rgb), 0.03) 0%, transparent 30%);
    z-index: 1;
}

.demo-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    position: relative;
    z-index: 2;
}

.demo-chat {
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    height: 500px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.demo-header {
    padding: 1rem;
    background-color: var(--bg-tertiary);
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--border-color);
}

.demo-title {
    color: var(--text-primary);
    font-weight: 600;
}

.demo-messages {
    flex-grow: 1;
    padding: 1rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.message {
    padding: 1rem;
    border-radius: var(--border-radius);
    max-width: 80%;
}

.message.user {
    background-color: var(--accent-primary);
    color: var(--text-primary);
    align-self: flex-end;
}

.message.bot {
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
    align-self: flex-start;
}

.demo-input {
    padding: 1rem;
    background-color: var(--bg-tertiary);
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 1rem;
}

.demo-input input {
    flex-grow: 1;
    padding: 0.8rem;
    background-color: var(--bg-quaternary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
}

.demo-input button {
    padding: 0.8rem 1.5rem;
    background-color: var(--accent-primary);
    color: var(--text-primary);
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.demo-input button:hover {
    background-color: var(--accent-secondary);
}

.demo-info {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.demo-info h3 {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
}

.demo-info ul {
    list-style: none;
    margin-bottom: 2rem;
}

.demo-info li {
    color: var(--text-secondary);
    margin-bottom: 0.8rem;
    display: flex;
    align-items: center;
}

.demo-info li::before {
    content: '✓';
    color: var(--accent-primary);
    font-weight: bold;
    margin-right: 0.5rem;
}

.cta-button.secondary {
    background-color: transparent;
    color: var(--accent-primary);
    border: 2px solid var(--accent-primary);
}

.cta-button.secondary:hover {
    background-color: var(--accent-primary);
    color: var(--text-primary);
}

/* Responsive Design */
@media (max-width: 992px) {
    .demo-container {
        grid-template-columns: 1fr;
    }

    .demo-chat {
        height: 400px;
    }
}

@media (max-width: 768px) {
    .trusted-by {
        flex-wrap: wrap;
        gap: 1rem;
        justify-content: center;
    }

    .trusted-logo {
        width: 100px;
        height: 50px;
    }

    .testimonials-slider {
        grid-template-columns: 1fr;
    }

    .message {
        max-width: 90%;
    }
}

@media (max-width: 576px) {
    h1 {
        font-size: 2.5rem;
    }

    h2 {
        font-size: 2rem;
    }

    .hero h1 {
        font-size: 3rem;
    }

    .hero .subtitle {
        font-size: 1.2rem;
    }

    .cta-button {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }
}
