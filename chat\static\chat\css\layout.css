/* layout.css - Main layout structure of the application */

/* Main content container */
.main-content {
    display: block;
    width: 100%;
    min-height: 100vh;
    position: relative;
    overflow: visible; /* Allow global scroll */
    background: transparent;
}

/* Chat section layout */
.chat-section {
    width: 100%;
    min-height: 100vh;
    display: block;
    position: relative;
    overflow: visible;
}

/* Chat container - base layout properties */
.chat-container {
    width: 100%;
    max-width: none;
    min-height: 100vh;
    display: block;
    position: relative;
    overflow: visible;
}

/* Chat box - main content area */
.chat-box {
    width: 80%;
    max-width: 80%;
    margin: 0 auto;
    position: relative;
    overflow: visible;
    padding-top: 60px; /* Space for fixed elements at top */
    padding-bottom: 150px; /* Space for input box and scroll area */
    min-height: 100vh; /* Ensure full viewport height */
}

/* Chat box content */
.chat-box-content {
    width: 100%;
    overflow-y: visible;
    padding: var(--spacing-md) 0;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

/* Chat box header */
.chat-box-header {
    display: flex;
    justify-content: center;
    padding: var(--spacing-xs) 0;
}

/* Empty state */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: var(--spacing-xl);
    text-align: center;
}

.empty-state-title {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.empty-state-description {
    color: var(--text-secondary);
    max-width: 600px;
    margin-bottom: var(--spacing-lg);
}

/* Responsive layout adjustments */
@media (max-width: 768px) {
    .chat-box-content {
        padding: var(--spacing-sm) 0;
        gap: var(--spacing-md);
    }

    .empty-state-title {
        font-size: var(--font-size-lg);
    }
}

/* UI Frame Layout - Consistent positioning for all fixed UI elements */
.ui-frame {
    --ui-element-spacing: 15px;
    --ui-element-height: 40px;
    position: fixed;
    z-index: 100;
    pointer-events: none; /* Allow clicks to pass through the frame itself */
}

/* Top right menu layout */
.top-right-menu {
    position: fixed;
    right: var(--ui-element-spacing);
    top: var(--ui-element-spacing);
    padding: 8px;
    display: flex;
    flex-direction: row;
    align-items: center;
    z-index: 100;
    gap: 8px; /* Consistent spacing between elements */
    background-color: none;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 20px;
    pointer-events: auto; /* Re-enable pointer events for this element */
}