/* input.css - Styles for input elements */

/* Chat form */
.chat-form {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  position: fixed;
  bottom: var(--spacing-lg);
  left: 0;
  right: 0;
  padding: 0;
  z-index: 10;
  background: transparent;
  margin: 0 !important;
  transition: var(--transition-theme);
  pointer-events: none; /* Make the container transparent to clicks */
}

/* Center the form vertically when chat is empty */
.chat-form.centered-vertically {
  bottom: 50%;
  transform: translateY(50%) !important;
}

/* Input box */
.input-box {
  transition: var(--transition-theme);
  padding: 0;
  background: transparent;
  max-width: 35%; /* Match legend width */
  width: 35%; /* Match legend width */
  position: relative;
  margin: 0 auto !important;
  pointer-events: auto; /* Ensure it's interactive */
}

/* Input container */
.input-container {
  background: rgba(var(--bg-quaternary-rgb), 0.4); /* Increased opacity for better contrast against darker chat background */
  border-radius: 26px;
  transition: none;
  display: flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-md); /* Increased horizontal padding */
  box-shadow: var(--card-shadow);
  width: 100%;
  height: 100px;
  max-height: 100px;
}

/* Input container - Light theme */
.light-theme .input-container {
  background: rgba(var(--bg-primary-rgb), 0.9); /* Use primary background with high opacity for better contrast against darker chat background */
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
}

.input-container:focus-within {
  box-shadow: none;
  border: 1px solid var(--accent-primary);
}

.light-theme .input-container:focus-within {
  border-color: var(--accent-primary);
  background: rgba(var(--bg-primary-rgb), 0.9); /* Maintain consistent background on focus */
  box-shadow: none;
}

/* Textarea */
.input-container textarea {
  font-family: var(--font-family-primary);
  font-size: var(--font-size);
  line-height: var(--line-height);
  padding: 0;
  flex: 1;
  min-height: 48px;
  height: 60px; /* Fixed height for better vertical alignment */
  max-height: 80px;
  overflow-y: auto;
  resize: none;
  margin: 0;
  background: transparent;
  border: none;
  outline: none;
  color: var(--text-primary);
  box-sizing: border-box;
  display: flex;
  align-items: center; /* Center text vertically */
  justify-content: center; /* Center text horizontally */
  text-align: center; /* Center the text content */
  vertical-align: middle; /* Additional vertical alignment */
  transition: var(--transition-theme);
}

.input-container textarea::placeholder {
  color: var(--text-dimmed);
  opacity: 0.7;
  font-style: italic;
}
.input-container textarea::-webkit-scrollbar-thumb {
  background-color: var(--accent-primary);
}

/* Input icons */
.input-icon {
  color: var(--text-dimmed);
  transition: none;
  background: transparent;
  border: none;
  cursor: pointer;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: auto var(--spacing-sm);
  opacity: 0.7;
  padding: 0;
}

.input-icon:hover {
  color: var(--accent-primary);
  opacity: 0.9;
}

.input-container:focus-within .input-icon {
  color: var(--accent-primary);
}
