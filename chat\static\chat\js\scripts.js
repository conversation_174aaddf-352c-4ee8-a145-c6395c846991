/**
 * Obtiene el valor de una cookie por su nombre.
 * @param {string} name - Nombre de la cookie.
 * @returns {string|null} - Valor de la cookie o null si no existe.
 */
function getCookie(name) {
  let cookieValue = null;
  if (document.cookie && document.cookie !== '') {
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.startsWith(name + '=')) {
        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
        break;
      }
    }
  }
  return cookieValue;
}

// Objeto principal para encapsular la logica del chat
const ChatBot = {
  csrftoken: getCookie('csrftoken'),
  isFirstMessage: true,
  currentChatId: null,
  /**
   * Inicializa los event listeners y el estado del chat al cargar la página.
   */
  initialize() {
    document.addEventListener('DOMContentLoaded', () => {
      const activeChatId = localStorage.getItem('activeChatId');
      if (performance.getEntriesByType('navigation')[0].type === 'reload' && activeChatId) {
        this.loadChat(activeChatId);
      } else {
        this.displayInitialState();
        localStorage.removeItem('activeChatId');
      }

      const newChatButton = document.querySelector('#new-chat-btn');
      if (newChatButton) {
        newChatButton.addEventListener('click', () => this.newChat());
      } else {
        console.error('New chat button not found');
      }

      const userInput = document.getElementById('user-input');
      userInput.addEventListener('keypress', (event) => {
        if (event.key === 'Enter' && !event.shiftKey) {
          event.preventDefault();
          this.sendMessage();
        }
      });

      userInput.addEventListener('focus', this.debounce(() => {
        window.scrollTo({
          top: document.body.scrollHeight,
          behavior: 'smooth',
        });
      }, 100));

      // Inicializar MathJax si está disponible
      if (window.MathJax) {
        MathJax.typesetPromise().catch(err => console.error("Error MathJax:", err));
      }
    });

    window.addEventListener('beforeunload', (event) => {
      const navigationEntries = performance.getEntriesByType('navigation');
      const isReload = navigationEntries.length > 0 && navigationEntries[0].type === 'reload';
      if (!isReload) {
        localStorage.removeItem('activeChatId');
      }
    });
  },

  /**
   * Funcion para debounce eventos y mejorar rendimiento.
   * @param {Function} func - Función a ejecutar.
   * @param {number} wait - Tiempo de espera en ms.
   * @returns {Function} - Función con debounce aplicada.
   */
  debounce(func, wait) {
    let timeout;
    return (...args) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), wait);
    };
  },

  /**
   * Muestra el estado inicial del chat.
   */
  displayInitialState() {
    const inputBox = document.querySelector('.input-box');
    if (!inputBox) {
      console.error('No se encontró .input-box en el DOM');
      return;
    }
    // Forzar la eliminación de clases previas y establecer el estado inicial
    inputBox.classList.remove('bottom');
    inputBox.classList.add('centered');
    this.clearChat();
    window.scrollTo(0, 0); // Fuerza el scroll al inicio para asegurar visibilidad
  },

  /**
   * Envía un mensaje al servidor y muestra la respuesta.
   */
  async sendMessage() {
    const inputField = document.getElementById('user-input');
    const chatBoxContent = document.getElementById('chat-box-content');
    const message = inputField.value.trim();
    if (!message) return;

    this.createUserMessage(message, chatBoxContent);
    this.scrollToNewMessage(chatBoxContent);
    this.toggleInputBox();

    if (this.isFirstMessage) {
      this.handleFirstMessage();
    }
    this.handleChatUpdates();

    inputField.value = '';
    inputField.disabled = true;

    try {
      const response = await fetch('/chat/api/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': this.csrftoken,
        },
        body: JSON.stringify({ message, chat_id: this.currentChatId }),
      });
      if (!response.ok) throw new Error('Error en la respuesta del servidor');
      const data = await response.json();
      this.processResponse(data, message, inputField);
    } catch (error) {
      console.error('Error enviando mensaje:', error);
      alert('Hubo un error al enviar el mensaje. Intenta de nuevo.');
      inputField.disabled = false;
    }
  },

  /**
   * Crea un mensaje del usuario en el chat.
   * @param {string} message - Mensaje del usuario.
   * @param {HTMLElement} chatBoxContent - Contenedor del chat.
   */
  createUserMessage(message, chatBoxContent) {
    const userMessage = document.createElement('div');
    userMessage.classList.add('chat-message', 'user-message', 'show');
    userMessage.setAttribute('role', 'log');
    userMessage.setAttribute('aria-live', 'polite');
    userMessage.textContent = message;
    chatBoxContent.appendChild(userMessage);
  },

  /**
   * Desplaza el chat al ultimo mensaje.
   * @param {HTMLElement} chatBoxContent - Contenedor del chat.
   */
  scrollToNewMessage(chatBoxContent) {
    setTimeout(() => {
      const lastMessage = chatBoxContent.querySelector('.chat-message:last-child');
      if (lastMessage) {
        lastMessage.scrollIntoView({ behavior: 'smooth', block: 'start' });
      } else {
        chatBoxContent.scrollTop = chatBoxContent.scrollHeight;
      }
    }, 100);
  },

  /**
   * Muestra u oculta el input box segun el estado.
   */
  toggleInputBox() {
    const inputBox = document.querySelector('.input-box');
    inputBox.style.display = 'flex';
  },

  /**
   * Maneja el primer mensaje del chat, ajustando la UI.
   */
  handleFirstMessage() {
    const inputBox = document.querySelector('.input-box');
    inputBox.classList.remove('centered');
    inputBox.classList.add('bottom');
    this.isFirstMessage = false;
  },

  /**
   * Actualiza la interfaz despues de enviar un mensaje.
   */
  handleChatUpdates() {
    const inputLegend = document.getElementById('input-legend');
    if (inputLegend) {
      inputLegend.style.display = 'none';
    }
  },
/**
 * Agrega un chat a la barra lateral.
 * @param {string} chat_id - ID del chat.
 * @param {string} [title] - Título del chat, por defecto 'New Chat'.
 */
prependChatToSidebar(chat_id, title = 'New Chat') {
  const chatContainer = document.querySelector('.chats-container');
  if (!chatContainer) {
    console.error('No se encontró el contenedor de chats');
    return;
  }
  if (!chatContainer.querySelector(`.chat-instance[data-chat-id="${chat_id}"]`)) {
    // Create chat instance container
    const newChat = document.createElement('div');
    newChat.classList.add('chat-instance');
    newChat.dataset.chatId = chat_id;
    newChat.onclick = () => this.loadChat(chat_id);

    // Create header container
    const headerDiv = document.createElement('div');
    headerDiv.classList.add('chat-instance-header');

    // Create icon container
    const iconDiv = document.createElement('div');
    iconDiv.classList.add('chat-icon');
    const icon = document.createElement('i');
    icon.className = 'bx bx-conversation';
    iconDiv.appendChild(icon);

    // Create info container
    const infoDiv = document.createElement('div');
    infoDiv.classList.add('chat-info');

    // Create title
    const titleP = document.createElement('p');
    titleP.textContent = title;

    // Create date
    const dateSpan = document.createElement('span');
    dateSpan.classList.add('chat-date');
    const now = new Date();
    dateSpan.textContent = now.toLocaleDateString('en-GB', { day: 'numeric', month: 'short', year: 'numeric' });

    // Append title and date to info container
    infoDiv.appendChild(titleP);
    infoDiv.appendChild(dateSpan);

    // Create actions container
    const actionsDiv = document.createElement('div');
    actionsDiv.classList.add('chat-actions');

    // Create rename button
    const renameButton = document.createElement('button');
    renameButton.classList.add('rename-chat-btn');
    renameButton.innerHTML = '<i class="bx bx-edit-alt"></i>';
    renameButton.addEventListener('click', (event) => this.renameChat(event, chat_id));

    // Create delete button
    const deleteButton = document.createElement('button');
    deleteButton.classList.add('delete-chat-btn');
    deleteButton.innerHTML = '<i class="bx bx-trash-alt"></i>';
    deleteButton.addEventListener('click', (event) => this.deleteChat(event, chat_id));

    // Append buttons to actions container
    actionsDiv.appendChild(renameButton);
    actionsDiv.appendChild(deleteButton);

    // Append all elements to their containers
    headerDiv.appendChild(iconDiv);
    headerDiv.appendChild(infoDiv);
    headerDiv.appendChild(actionsDiv);
    newChat.appendChild(headerDiv);

    // Add to the DOM
    chatContainer.prepend(newChat);
  }
  chatContainer.scrollTop = 0;
  localStorage.setItem('activeChatId', chat_id);
},

/**
 * Procesa la respuesta del servidor y actualiza el chat.
 * @param {Object} data - Datos de la respuesta del servidor.
 * @param {string} message - Mensaje original del usuario.
 * @param {HTMLInputElement} inputField - Campo de entrada.
 */
processResponse(data, message, inputField) {
  const chatBoxContent = document.getElementById('chat-box-content');
  const botMessage = this.createBotMessageElement(data.response);

  // Asegurarse de que el mensaje comience con opacidad 0
  botMessage.style.opacity = '0';
  chatBoxContent.appendChild(botMessage);

  // Asegurar que el formulario de chat permanezca centrado
  const chatForm = document.querySelector('.chat-form');
  if (chatForm) {
    chatForm.style.margin = '0 auto';
    chatForm.style.left = '0';
    chatForm.style.right = '0';
    chatForm.style.transform = 'none';
  }

  if (data.chat_id) {
    if (!this.currentChatId) {
      // Use the title from the backend if available, otherwise use default
      this.prependChatToSidebar(data.chat_id, data.title || 'New Chat');
    }
    this.currentChatId = data.chat_id;
    localStorage.setItem('activeChatId', data.chat_id);
  }

  // Forzar un reflow para que la transición CSS se aplique
  void botMessage.offsetWidth;

  // Cambiar la opacidad a 1 para activar la animación
  botMessage.style.opacity = '1';

  // No hacemos scroll automático cuando el bot responde
  if (window.MathJax) {
    MathJax.typesetPromise([botMessage]);
  }

  inputField.disabled = false;
},
  /**
   * Carga un chat existente desde el servidor.
   * @param {string} chatId - ID del chat a cargar.
   */
  async loadChat(chatId) {
    const chatBoxContent = document.getElementById('chat-box-content');
    const inputBox = document.querySelector('.input-box');
    if (!chatBoxContent || !inputBox) {
        console.error('Missing required DOM elements');
        alert('Error: Chat interface not properly loaded.');
        return;
    }

    // Asegurar que el formulario de chat permanezca centrado
    const chatForm = document.querySelector('.chat-form');
    if (chatForm) {
      chatForm.style.margin = '0 auto';
      chatForm.style.left = '0';
      chatForm.style.right = '0';
      chatForm.style.transform = 'none';
    }

    if (this.currentChatId && this.currentChatId !== chatId) {
        this.clearChat();
    }
    chatBoxContent.innerHTML = '';

    try {
        const response = await fetch(`/chat/load_chat_messages/${chatId}/`);
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Failed to load chat: ${response.status} - ${errorText || 'Unknown error'}`);
        }

        const data = await response.json();
        if (!data.messages || !Array.isArray(data.messages)) {
            throw new Error(data.error || 'Invalid chat data received');
        }

        this.currentChatId = chatId;
        localStorage.setItem('activeChatId', chatId);

        inputBox.classList.remove('centered');
        inputBox.classList.add('bottom');
        this.isFirstMessage = false;

        data.messages.forEach((msg) => {
            if (!msg || typeof msg !== 'object' || !msg.content) return; // Skip invalid messages
            const messageElement = msg.sender === 'bot'
                ? this.createBotMessageElement(msg.content)
                : document.createElement('div');
            if (msg.sender !== 'bot') {
                messageElement.classList.add('chat-message', 'user-message', 'show');
                messageElement.textContent = msg.content;
            } else {
                messageElement.style.opacity = '1';
            }
            chatBoxContent.appendChild(messageElement);
        });

        const finalizeDisplay = () => {
            // No hacemos scroll automático al cargar mensajes antiguos
            // const lastMessage = chatBoxContent.querySelector('.chat-message:last-child');
            // if (lastMessage) {
            //     lastMessage.scrollIntoView({ behavior: 'smooth', block: 'start' });
            // } else {
            //     chatBoxContent.scrollTop = chatBoxContent.scrollHeight;
            // }
            const userInput = document.getElementById('user-input');
            if (userInput) userInput.focus();
        };

        if (window.MathJax) {
            MathJax.typesetPromise().then(finalizeDisplay).catch((err) => {
                console.error('MathJax error:', err);
                finalizeDisplay(); // Proceed even if MathJax fails
            });
        } else {
            finalizeDisplay();
        }
    } catch (error) {
        console.error('Error loading chat:', error.message, error.stack);
        alert(`No se pudo cargar el chat: ${error.message}`);
    }
},
/**
 * Elimina un chat del servidor y la UI.
 * @param {Event} event - Evento del click.
 * @param {string} chatId - ID del chat a eliminar.
 */
async deleteChat(event, chatId) {
  event.stopPropagation();

  try {
    const response = await fetch(`/chat/delete_chat/${chatId}/`, {
      method: 'DELETE',
      headers: {
        'X-CSRFToken': this.csrftoken,
        'Content-Type': 'application/json',
      },
    });
    const data = await response.json();

    if (data.status === 'success') {
      const chatInstance = document.querySelector(`.chat-instance[data-chat-id="${chatId}"]`);
      if (chatInstance) {
        chatInstance.remove();
      }

      // Verificar si el chat eliminado era el actual
      if (this.currentChatId === chatId) {
        localStorage.removeItem('activeChatId');
        this.currentChatId = null;
        this.clearChat();
      }

      // Comprobar si quedan chats y ajustar el estado del UI
      const remainingChats = document.querySelectorAll('.chat-instance');
      if (remainingChats.length === 0) {
        this.isFirstMessage = true; // Asegurar que el próximo mensaje sea tratado como el primero
        this.displayInitialState(); // Mostrar el estado inicial explícitamente
      }
    } else {
      alert('Error al eliminar el chat: ' + data.message);
    }
  } catch (error) {
    console.error('Error eliminando el chat:', error);
    alert('Error al eliminar el chat.');
  }
},

  /**
   * Crea un nuevo chat desde cero.
   */
  newChat() {
    this.clearChat();
    this.currentChatId = null;
    this.isFirstMessage = true;
    this.displayInitialState();
  },

  /**
   * Renombra un chat existente.
   * @param {Event} event - Evento del click.
   * @param {string} chatId - ID del chat a renombrar.
   */
  async renameChat(event, chatId) {
    event.stopPropagation();

    // Obtener el elemento del chat
    const chatElement = document.querySelector(`.chat-instance[data-chat-id="${chatId}"]`);
    if (!chatElement) return;

    // Obtener el elemento del título
    const titleElement = chatElement.querySelector('.chat-info p');
    if (!titleElement) return;

    // Guardar el título actual
    const currentTitle = titleElement.textContent;

    // Crear campo de entrada para el nuevo título
    const inputElement = document.createElement('input');
    inputElement.type = 'text';
    inputElement.className = 'chat-rename-input';
    inputElement.value = currentTitle;
    inputElement.maxLength = 50;

    // Reemplazar el título con el campo de entrada
    titleElement.style.display = 'none';
    titleElement.parentNode.insertBefore(inputElement, titleElement);

    // Enfocar el campo de entrada
    inputElement.focus();
    inputElement.select();

    // Función para guardar el nuevo título
    const saveNewTitle = async () => {
      const newTitle = inputElement.value.trim();

      // Validar que el título no esté vacío
      if (!newTitle) {
        inputElement.value = currentTitle;
        return;
      }

      // Restaurar la visualización del título
      titleElement.style.display = '';
      inputElement.remove();

      // Actualizar el título en la UI temporalmente
      titleElement.textContent = newTitle;

      try {
        // Enviar la solicitud al servidor
        const response = await fetch(`/chat/update_chat_title/${chatId}/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': this.csrftoken,
          },
          body: JSON.stringify({ title: newTitle }),
        });

        const data = await response.json();

        if (data.status !== 'success') {
          // Si hay un error, restaurar el título anterior
          titleElement.textContent = currentTitle;
          alert(`Error al renombrar el chat: ${data.message}`);
        }
      } catch (error) {
        console.error('Error renombrando chat:', error);
        // Restaurar el título anterior en caso de error
        titleElement.textContent = currentTitle;
        alert(`No se pudo renombrar el chat: ${error.message}`);
      }
    };

    // Guardar al presionar Enter o al perder el foco
    inputElement.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        saveNewTitle();
      } else if (e.key === 'Escape') {
        e.preventDefault();
        titleElement.style.display = '';
        inputElement.remove();
      }
    });

    inputElement.addEventListener('blur', saveNewTitle);
  },

/**
   * Crea un elemento de mensaje del bot.
   * @param {string} messageContent - Contenido del mensaje.
   * @returns {HTMLElement} - Elemento del mensaje del bot.
   */
createBotMessageElement(messageContent) {
  const botMessage = document.createElement('div');
  botMessage.classList.add('chat-message', 'bot-message', 'show');

  const thinkMatch = messageContent.match(/<think>([\s\S]*?)<\/think>/i);
  let thinkContent = '';
  let mainContent = messageContent;

  if (thinkMatch) {
    thinkContent = thinkMatch[1];
    mainContent = messageContent.replace(thinkMatch[0], '');
    this.addThinkContent(botMessage, thinkContent);
  }

  // Asegurarse de crear el elemento antes de usarlo
  const mainContentDiv = document.createElement('div');
  mainContentDiv.innerHTML = this.formatLaTeX(mainContent); // ✅ Se usa formatLaTeX correctamente
  botMessage.appendChild(mainContentDiv);

  // Procesar bloques de código para asegurar que el HTML se escape correctamente
  setTimeout(() => {
    if (window.escapeHtmlInCodeBlocks) {
      window.escapeHtmlInCodeBlocks();
    }

    if (window.forceJavaScriptHighlighting) {
      window.forceJavaScriptHighlighting();
    }

    // Manejar bloques de código con etiquetas span anidadas
    if (window.handleStyledCodeBlocks) {
      window.handleStyledCodeBlocks();
    }

    // Procesar específicamente entidades HTML en bloques de código
    document.querySelectorAll('pre code').forEach(function(codeElement) {
      const content = codeElement.textContent || codeElement.innerHTML;

      // Guardar el contenido original para la funcionalidad de copia
      if (!codeElement.hasAttribute('data-original-content')) {
        codeElement.setAttribute('data-original-content', content);
      }

      // Detectar si es código JavaScript que contiene HTML
      const hasHtmlTags = /<[a-z][\s\S]*>/i.test(content);

      // Verificar si hay palabras clave de JavaScript
      const hasJsKeywords = /\b(function|const|let|var|if|else|for|while|return|class|this|new)\b/.test(content);

      // Detectar si tiene múltiples etiquetas span con estilos (caso especial)
      const hasMultipleSpans = (content.match(/<span/g) || []).length > 3;
      const hasColorStyles = content.includes('style="color:') || content.includes('style="font-weight:');
      const isStyledCode = hasMultipleSpans && hasColorStyles;

      // Detectar patrones específicos de código con estilos de color
      const hasColorHexCodes = (
        content.includes('#569CD6') || // Azul
        content.includes('#C586C0') || // Morado
        content.includes('#4EC9B0') || // Verde azulado
        content.includes('#CE9178') || // Naranja
        content.includes('#B5CEA8') || // Verde claro
        content.includes('#DCDCAA')    // Amarillo
      );

      if ((hasJsKeywords && hasHtmlTags) || isStyledCode || hasColorHexCodes) {
        // Es probablemente JavaScript con HTML o código con estilos
        codeElement.className = 'language-javascript';

        // Decodificar y escapar el HTML
        const decodedElement = document.createElement('textarea');
        decodedElement.innerHTML = content;
        const decodedContent = decodedElement.value;

        const tempElement = document.createElement('div');
        tempElement.textContent = decodedContent;
        codeElement.innerHTML = tempElement.innerHTML;

        // Re-aplicar el resaltado
        if (window.hljs) {
          hljs.highlightElement(codeElement);
        }
      } else if (/&quot;|&lt;|&gt;|&amp;/g.test(content)) {
        // Decodificar entidades HTML
        const decodedElement = document.createElement('textarea');
        decodedElement.innerHTML = content;
        const decodedContent = decodedElement.value;

        // Reemplazar el contenido con la versión decodificada
        codeElement.textContent = decodedContent;

        // Re-aplicar el resaltado
        if (window.hljs) {
          hljs.highlightElement(codeElement);
        }
      }
    });
  }, 100);

  if (messageContent.length >= 600) {
    const footer = this.createFooter({ response: messageContent }, botMessage);
    botMessage.appendChild(footer);
  }

  return botMessage;
},

/**
 * Agrega contenido de pensamiento al mensaje del bot con un header "Think..."
 * @param {HTMLElement} botMessage - Elemento del mensaje.
 * @param {string} thinkContent - Contenido del pensamiento.
 * @param {Object} [options] - Opciones de personalización (opcional).
 */
addThinkContent(botMessage, thinkContent, options = {}) {
  const thinkContainer = document.createElement('div');
  thinkContainer.className = 'think-container';

  const thinkHeader = document.createElement('div');
  thinkHeader.className = 'think-header';
  thinkHeader.textContent = 'Think...';

  // Formatear el contenido del pensamiento usando el mismo formateador que los mensajes normales
  const formattedThinkContent = this.formatLaTeX ? this.formatLaTeX(thinkContent) : thinkContent;

  const thinkBubble = document.createElement('div');
  thinkBubble.className = 'think-bubble';
  thinkBubble.innerHTML = formattedThinkContent; // Usar innerHTML para permitir formato

  const toggleButton = document.createElement('button');
  toggleButton.className = 'think-toggle';
  toggleButton.setAttribute('aria-label', 'Toggle thought content');
  toggleButton.innerHTML = '<i class="fa-solid fa-chevron-down"></i>';

  const mistOverlay = document.createElement('div');
  mistOverlay.className = 'mist-overlay';

  // Hacer que el header también pueda expandir/colapsar el contenido
  const toggleExpansion = function() {
    const isExpanded = thinkContainer.classList.toggle('expanded');
    const icon = toggleButton.querySelector('i');

    icon.classList.toggle('fa-chevron-down');
    icon.classList.toggle('fa-chevron-up');

    if (isExpanded) {
      thinkBubble.style.maxHeight = '200px';
      thinkBubble.classList.add('scrollable');
      mistOverlay.classList.add('hidden');
      thinkHeader.classList.add('expanded');
    } else {
      thinkBubble.style.maxHeight = '0';
      thinkBubble.classList.remove('scrollable');
      mistOverlay.classList.remove('hidden');
      thinkHeader.classList.remove('expanded');
    }

    // Procesar matemáticas si MathJax está disponible
    if (window.MathJax) {
      MathJax.typesetPromise([thinkBubble]);
    }
  };

  toggleButton.addEventListener('click', toggleExpansion);
  thinkHeader.addEventListener('click', toggleExpansion);

  thinkContainer.appendChild(thinkHeader);
  thinkContainer.appendChild(thinkBubble);
  thinkContainer.appendChild(mistOverlay);
  thinkContainer.appendChild(toggleButton);
  botMessage.appendChild(thinkContainer);

  // Aplicar tema claro/oscuro
  const isDarkTheme = document.body.classList.contains('dark-theme');
  const isLightTheme = document.body.classList.contains('light-theme');

  if (isLightTheme) {
    thinkContainer.classList.add('light-theme');
  }

  if (options.theme) {
    thinkContainer.classList.add(`theme-${options.theme}`);
  }

  // Procesar código y matemáticas dentro del contenido de pensamiento
  setTimeout(() => {
    if (window.escapeHtmlInCodeBlocks) {
      window.escapeHtmlInCodeBlocks();
    }

    if (window.MathJax) {
      MathJax.typesetPromise([thinkBubble]);
    }
  }, 100);

  // Establecer altura inicial (completamente colapsado)
  thinkBubble.style.maxHeight = '0';
},

  /**
   * Agrega el contenido principal al mensaje del bot.
   * @param {HTMLElement} botMessage - Elemento del mensaje.
   * @param {string} mainContent - Contenido principal.
   */
  addMainContent(botMessage, mainContent) {
    const mainContentDiv = document.createElement('div');

    // Process the content using formatLaTeX
    const processedContent = this.formatLaTeX(mainContent);

    // Assign the processed content to innerHTML
    mainContentDiv.innerHTML = processedContent;

    // Append the processed content to the bot message
    botMessage.appendChild(mainContentDiv);
  },

  /**
   * Función auxiliar para escapar HTML
   * @param {string} str - Cadena a escapar
   * @returns {string} - Cadena escapada
   */
  escapeHtml(text) {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  },

  /**
   * Crea el pie de pagina para mensajes largos.
   * @param {Object} data - Datos del mensaje.
   * @param {HTMLElement} botMessage - Elemento del mensaje.
   * @returns {HTMLElement} - Elemento del footer.
   */
  createFooter(data, botMessage) {
    const footer = document.createElement('div');
    footer.classList.add('bot-footer');

    const footerText = document.createElement('span');
    footerText.classList.add('footer-text');
    footerText.textContent = 'The model can make mistakes';

    const MIN_CHARS_FOR_ICONS = 1200;
    if (data.response.length >= MIN_CHARS_FOR_ICONS) {
      const iconsContainer = document.createElement('div');
      iconsContainer.classList.add('footer-icons');

      const copyIcon = document.createElement('span');
      copyIcon.classList.add('icon', 'copy-icon');
      copyIcon.innerHTML = '<i class="fas fa-copy"></i>';
      copyIcon.title = 'Copiar';
      copyIcon.addEventListener('click', () => {
        navigator.clipboard.writeText(data.response)
          .then(() => alert('Texto copiado'))
          .catch((err) => console.error('Error copiando el texto:', err));
      });



      const shareIcon = document.createElement('span');
      shareIcon.classList.add('icon', 'share-icon');
      shareIcon.innerHTML = '<i class="fa-solid fa-bookmark"></i>';
      shareIcon.title = 'Archivar';
      shareIcon.addEventListener('click', () => {
        if (navigator.share) {
          navigator.share({
            title: 'Mensaje del bot',
            text: data.response,
            url: window.location.href,
          });
        } else {
          alert('La API de compartir no está disponible');
        }
      });

      iconsContainer.appendChild(copyIcon);
      iconsContainer.appendChild(shareIcon);
      footer.appendChild(footerText);
      footer.appendChild(iconsContainer);
    } else {
      footer.appendChild(footerText);
    }
    return footer;
  },

  /**
   * Limpia el contenido del chat.
   */
  clearChat() {
    const chatBox = document.getElementById('chat-box-content');
    if (chatBox) {
      chatBox.innerHTML = '';
    } else {
      console.error('No se encontró el chat-box-content');
    }
  },

  /**
   * Resetea la memoria del chat actual en el servidor.
   */
  async resetChat() {
    this.clearChat();
    if (this.currentChatId) {
      try {
        await fetch(`/chat/reset_chat_memory/${this.currentChatId}/`, {
          method: 'POST',
          headers: { 'X-CSRFToken': this.csrftoken },
        });
      } catch (error) {
        console.error('Error reseteando memoria del chat:', error);
      }
    }
  },

  /**
   * Formatea el texto con soporte para LaTeX, Markdown y resaltado de sintaxis.
   * @param {string} text - Texto a formatear.
   * @returns {string} - Texto formateado como HTML.
   */
  formatLaTeX(text) {
    console.log('Texto original antes de procesar:', text);

    // ******** EXTRAER BLOQUES DE CÓDIGO ********
    let codeBlocks = [];
    text = text.replace(/```([a-z]*)\n([\s\S]*?)```/g, (match, language, code) => {
      language = language.trim().toLowerCase() || 'plaintext';

      // Detectar si el código contiene etiquetas HTML o códigos de color hex
      const hasHtmlTags = /<[a-z][\s\S]*>/i.test(code);
      const hasColorHexCodes = (
        code.includes('#569CD6') || // Azul
        code.includes('#C586C0') || // Morado
        code.includes('#4EC9B0') || // Verde azulado
        code.includes('#CE9178') || // Naranja
        code.includes('#B5CEA8') || // Verde claro
        code.includes('#DCDCAA')    // Amarillo
      );
      const hasSpanTags = code.includes('<span style="color:');

      // Si el código contiene etiquetas HTML con estilos de color, forzar JavaScript
      if (hasSpanTags || (hasHtmlTags && hasColorHexCodes)) {
        language = 'javascript';
      }

      // Escapar el HTML para mostrarlo como texto plano
      const escapedCode = this.escapeHtml(code.trim());
      codeBlocks.push(`<pre><code class="language-${language}">${escapedCode}</code></pre>`);
      return `%%CODE${codeBlocks.length - 1}%%`;
    });

    // ******** EXTRAER BLOQUES MATEMÁTICOS ********
    let mathBlocks = [];
    text = text.replace(
      /(\$\$[\s\S]*?\$\$|\$[\s\S]*?\$|\\\[[\s\S]*?\\\]|\\\([\s\S]*?\\\)|\\begin\{equation\}[\s\S]*?\\end\{equation\})/g,
      (match) => {
        mathBlocks.push(match);
        return `%%MATH${mathBlocks.length - 1}%%`;
      }
    );

    // ******** EXTRAER TABLAS MARKDOWN ********
    let tableBlocks = [];
    text = text.replace(/^\|(.+)\|\s*\n\|([-:]+\|)+\s*\n((?:\|.*\|\s*\n)+)/gm, (match) => {
      tableBlocks.push(match);
      return `%%TABLE${tableBlocks.length - 1}%%`;
    });

    // ******** PROCESAR OTROS ELEMENTOS ********
    text = text.replace(/`([^`]+)`/g, '<code>$1</code>');

    // ******** PROCESAR ENCABEZADOS ********
    text = text.replace(/^(#{1,6})\s+(.*)$/gm, (match, hashes, content) => {
      const level = hashes.length;
      const marginTop = level === 1 ? '32px' : '28px';
      return `<h${level} style="margin-top: ${marginTop}; margin-bottom: 16px;">${content.trim()}</h${level}>`;
    });

    text = text.replace(/\\section\*?\{([^\n}]+)(?:\}|$)/g, '<h1 style="margin-top: 32px; margin-bottom: 16px;">$1</h1>');
    text = text.replace(/\\subsection\*?\{([^\n}]+)(?:\}|$)/g, '<h2 style="margin-top: 28px; margin-bottom: 16px;">$1</h2>');
    text = text.replace(/\\subsubsection\*?\{([^\n}]+)(?:\}|$)/g, '<h3 style="margin-top: 24px; margin-bottom: 16px;">$1</h3>');

    // ******** PROCESAR TABLAS EN LATEX ********
    text = text.replace(/\\begin\{table\}[\s\S]*?\\begin\{tabular\}\{([\s\S]*?)\}([\s\S]*?)\\end\{tabular\}[\s\S]*?\\end\{table\}/g, (match, columnFormat, tableContent) => {
      let rows = tableContent.trim().split('\\hline').filter(row => row.trim() !== '');
      let htmlTable = '<table style="margin: 24px 0; border-collapse: collapse; width: 100%; border: 1px solid rgba(255, 255, 255, 0.2);">';

      rows.forEach((row, index) => {
        let columns = row.trim().split('&').map(col => col.trim().replace(/\\\\$/, ''));
        htmlTable += index === 0 ? '<thead><tr>' : '<tr>';
        columns.forEach(col => {
          col = col.replace(/\\textbf\{(.*?)\}/g, '<strong>$1</strong>');
          col = col.replace(/\\textit\{(.*?)\}/g, '<em>$1</em>');
          if (index === 0) {
            htmlTable += `<th style="padding: 12px; text-align: left; border: 1px solid rgba(255, 255, 255, 0.2); border-bottom: 2px solid rgba(126, 231, 135, 0.5);">${col}</th>`;
          } else {
            htmlTable += `<td style="padding: 10px; border: 1px solid rgba(255, 255, 255, 0.2);">${col}</td>`;
          }
        });
        htmlTable += index === 0 ? '</tr></thead><tbody>' : '</tr>';
      });

      htmlTable += '</tbody></table>';
      return htmlTable;
    });

    // ******** PROCESAR LISTAS ANIDADAS EN LATEX (RECURSIVAMENTE) ********
    function processLists(latex) {
      return latex.replace(/\\begin\{(enumerate|itemize)\}([\s\S]*?)\\end\{\1\}/g, (match, type, content) => {
        let tag = type === 'enumerate' ? 'ol' : 'ul';
        let items = content
          .replace(/^\s*\\item\s*/gm, '%%ITEM%%')
          .split('%%ITEM%%')
          .filter(item => item.trim().length > 0)
          .map(item => `<li style="margin-bottom: 8px;">${processLists(item).trim()}</li>`);
        return `<${tag} style="margin: 16px 0; padding-left: 24px;">${items.join('')}</${tag}>`;
      });
    }
    text = processLists(text);

    // ******** PROCESAR LISTAS EN MARKDOWN (RECURSIVAMENTE) ********
    function processMarkdownLists(text) {
      const lines = text.split('\n');

      function parseList(lines, indent) {
        let items = [];
        let i = 0;
        let listIsNumbered = false;

        while (i < lines.length) {
          const line = lines[i];
          // Modified regex to handle empty list items (where the marker is not followed by content)
          const match = line.match(/^(\s*)([\*\-\+]|\d+\.)\s+(.+)$/);
          if (!match) {
            // Check if this is an empty list item (just a marker with no content)
            const emptyMatch = line.match(/^(\s*)([\*\-\+]|\d+\.)\s*$/);
            if (emptyMatch) {
              // This is an empty list item, look ahead to see if the next line has content
              if (i + 1 < lines.length && lines[i + 1].trim() !== '' &&
                  !lines[i + 1].match(/^(\s*)([\*\-\+]|\d+\.)\s+/)) {
                // Use the content from the next line
                const currentIndent = emptyMatch[1].length;
                const currentItemIsNumbered = /^\d+\./.test(emptyMatch[2]);
                if (i === 0) listIsNumbered = currentItemIsNumbered;

                // Get content from the next line
                const nextLineContent = lines[i + 1].trim();
                items.push(nextLineContent);

                // Skip the next line since we've used its content
                i += 2;
                continue;
              }
            }
            i++;
            continue;
          }

          const currentIndent = match[1].length;
          if (currentIndent < indent) break;

          let itemContent = match[3];
          const currentItemIsNumbered = /^\d+\./.test(match[2]);
          if (i === 0) listIsNumbered = currentItemIsNumbered;

          i++;

          let subLines = [];
          while (i < lines.length) {
            const nextLine = lines[i];
            const nextMatch = nextLine.match(/^(\s*)([\*\-\+]|\d+\.)\s+(.+)$/);
            const nextIndent = nextMatch ? nextMatch[1].length : 0;

            if (nextLine.trim() !== '' && (nextIndent > currentIndent ||
                (!nextMatch && nextLine.startsWith(' '.repeat(currentIndent + 1))))) {
              subLines.push(nextLine);
              i++;
            } else {
              break;
            }
          }

          if (subLines.length > 0) {
            const subIndent = subLines[0].match(/^(\s*)/)[1].length;
            const { html: sublistHtml } = parseAndRenderList(subLines, subIndent);
            itemContent += sublistHtml;
          }

          items.push(itemContent);
        }

        return { items, isNumbered: listIsNumbered };
      }

      function parseAndRenderList(lines, indent) {
        const { items, isNumbered } = parseList(lines, indent);
        const tag = isNumbered ? 'ol' : 'ul';
        const html = `<${tag} style="margin: 16px 0; padding-left: 24px;">${items.map(item => `<li style="margin-bottom: 8px;">${item}</li>`).join('')}</${tag}>`;
        return { html, parsedLines: items.length };
      }

      let result = [];
      let i = 0;

      while (i < lines.length) {
        // Check for both standard list items and empty list items (with content on next line)
        if (/^\s*([\*\-\+]|\d+\.)\s+/.test(lines[i]) ||
            (/^\s*([\*\-\+]|\d+\.)\s*$/.test(lines[i]) &&
             i + 1 < lines.length && lines[i + 1].trim() !== '')) {
          let listBlock = [];
          const startIndent = lines[i].match(/^(\s*)/)[1].length;

          while (i < lines.length) {
            const line = lines[i].trimEnd();
            if (line.trim() === '') {
              let nextNonEmpty = i + 1;
              while (nextNonEmpty < lines.length && lines[nextNonEmpty].trim() === '') nextNonEmpty++;
              if (nextNonEmpty < lines.length && /^\s*([\*\-\+]|\d+\.)\s+/.test(lines[nextNonEmpty])) {
                listBlock.push(line);
                i++;
                continue;
              }
              break;
            } else if (/^\s*([\*\-\+]|\d+\.)\s+/.test(line) || /^\s*([\*\-\+]|\d+\.)\s*$/.test(line)) {
              listBlock.push(line);

              // If this is an empty list item, check if the next line should be included
              if (/^\s*([\*\-\+]|\d+\.)\s*$/.test(line) &&
                  i + 1 < lines.length &&
                  lines[i + 1].trim() !== '' &&
                  !lines[i + 1].match(/^\s*([\*\-\+]|\d+\.)/)) {
                // Add the next line as part of this list item
                listBlock.push(lines[i + 1]);
                i += 2; // Skip both lines
                continue;
              }
              i++;
            } else if (line.match(/^(\s*)/)[1].length > startIndent) {
              listBlock.push(line);
              i++;
            } else {
              break;
            }
          }

          if (listBlock.length > 0) {
            const { html } = parseAndRenderList(listBlock, startIndent);
            result.push(html);
          }
        } else {
          result.push(lines[i]);
          i++;
        }
      }

      return result.join('\n');
    }
    text = processMarkdownLists(text);

    // ******** PROCESAR CITAS (BLOCKQUOTE) ********
    text = text.replace(/^>\s*(.*)$/gm, '<blockquote>$1</blockquote>');
    text = text.replace(/<\/blockquote>\s*<blockquote>/g, '<br>');

    // ******** PROCESAR NEGRITAS Y CURSIVAS ********
    text = text.replace(/\\textbf\{(.*?)\}/g, '<strong>$1</strong>');
    text = text.replace(/\\textit\{(.*?)\}/g, '<em>$1</em>');
    text = text.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>');
    text = text.replace(/(?<!\*)\*(?!\*)(.+?)(?<!\*)\*(?!\*)/g, '<em>$1</em>');
    text = text.replace(/\_\_(.+?)\_\_/g, '<strong>$1</strong>');
    text = text.replace(/\_(.+?)\_/g, '<em>$1</em>');

    // ******** PROCESAR LÍNEA HORIZONTAL ********
    text = text.replace(/^\s*---\s*$/gm, '<hr style="margin: 24px 0; height: 1px; border: none; background: var(--border-color);">');
    text = text.replace(/^\s*\*\*\*\s*$/gm, '<hr style="margin: 24px 0; height: 1px; border: none; background: var(--border-color);">');
    text = text.replace(/^\s*___\s*$/gm, '<hr style="margin: 24px 0; height: 1px; border: none; background: var(--border-color);">');

    // ******** PROCESAR SALTOS DE LÍNEA Y PÁRRAFOS ********
    text = text.split(/\n\s*\n/).map(paragraph => {
      paragraph = paragraph.trim();
      if (
        paragraph === '' ||
        /^%%(CODE|MATH|TABLE)\d+%%$/.test(paragraph) ||
        /^\s*(<\/?(h\d|ul|ol|li|div|p|blockquote|table|pre|hr))/i.test(paragraph)
      ) {
        return paragraph;
      }
      return `<p>${paragraph}</p>`;
    }).join('\n');

    text = text.replace(/\\newline/g, '<br>');
    text = text.replace(/\\\\/g, '<br>');

    // ******** RENDERIZAR TABLAS MARKDOWN ********
    text = text.replace(/%%TABLE(\d+)%%/g, (match, index) => {
      const tableContent = tableBlocks[Number(index)];
      const lines = tableContent.trim().split('\n');
      if (lines.length < 3) return tableContent;

      const headerRow = lines[0];
      const separatorRow = lines[1];
      const bodyRows = lines.slice(2);

      const headers = headerRow.split('|').filter(cell => cell.trim() !== '').map(cell => cell.trim());
      const alignments = separatorRow.split('|').filter(cell => cell.trim() !== '').map(cell => {
        const trimmed = cell.trim();
        if (trimmed.startsWith(':') && trimmed.endsWith(':')) return 'center';
        if (trimmed.endsWith(':')) return 'right';
        return 'left';
      });

      let htmlTable = '<table style="margin: 24px 0; border-collapse: collapse; width: 100%; border: 1px solid var(--border-color);">';
      htmlTable += '<thead><tr>';
      headers.forEach((header, index) => {
        header = header.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>');
        htmlTable += `<th style="padding: 12px; text-align: ${alignments[index] || 'left'}; border: 1px solid var(--border-color); border-bottom: 2px solid var(--accent-primary);">${header}</th>`;
      });
      htmlTable += '</tr></thead><tbody>';

      bodyRows.forEach(row => {
        if (row.trim() === '') return;
        const cells = row.split('|').filter(cell => cell.trim() !== '').map(cell => cell.trim());
        htmlTable += '<tr>';
        cells.forEach((cell, index) => {
          cell = cell.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>');
          htmlTable += `<td style="padding: 10px; text-align: ${alignments[index] || 'left'}; border: 1px solid var(--border-color);">${cell}</td>`;
        });
        htmlTable += '</tr>';
      });

      htmlTable += '</tbody></table>';
      return htmlTable;
    });

    // ******** MEJORAR LEGIBILIDAD DE ENLACES ********
    text = text.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" style="color: var(--accent-color); text-decoration: none; border-bottom: 1px dotted var(--accent-color);">$1</a>');
    text = text.replace(/(https?:\/\/[^\s<]+)/g, match => {
      if (match.indexOf('</a>') === -1) {
        return `<a href="${match}" style="color: var(--accent-color); text-decoration: none; border-bottom: 1px dotted var(--accent-color);">${match}</a>`;
      }
      return match;
    });

    // ******** PROCESAR MATRICES LATEX ********
    text = text.replace(/\\begin\{(pmatrix|bmatrix|vmatrix|Vmatrix|matrix)\}([\s\S]*?)\\end\{\1\}/g, (match, matrixType, content) => {
      let delimiters;
      switch (matrixType) {
        case 'pmatrix': delimiters = ['(', ')']; break;
        case 'bmatrix': delimiters = ['[', ']']; break;
        case 'vmatrix': delimiters = ['|', '|']; break;
        case 'Vmatrix': delimiters = ['‖', '‖']; break;
        case 'matrix': delimiters = ['', '']; break;
        default: delimiters = ['', ''];
      }

      const rows = content.trim().split('\\\\').map(row => row.trim());
      let matrixHTML = '<div class="math-block">\\[ ' + delimiters[0] + ' \\begin{array}{';
      const columnCount = (rows[0].match(/&/g) || []).length + 1;
      matrixHTML += 'c'.repeat(columnCount) + '} ';

      rows.forEach((row, index) => {
        matrixHTML += row + (index < rows.length - 1 ? ' \\\\ ' : '');
      });

      matrixHTML += ' \\end{array} ' + delimiters[1] + ' \\]</div>';
      return matrixHTML;
    });

    // ******** REINSERTAR BLOQUES MATEMÁTICOS ********
    text = text.replace(/%%MATH(\d+)%%/g, (match, index) => {
      let mathContent = mathBlocks[Number(index)];
      if (mathContent.startsWith('$$') || mathContent.startsWith('\\[') || mathContent.startsWith('\\begin{equation}')) {
        mathContent = mathContent.replace(/(^\$\$|^\\\[|^\\begin\{equation\})/, '').replace(/(\$\$$|\\\]$|\\end\{equation\}$)/, '');
        return `<div class="math-block">\\[ ${mathContent} \\]</div>`;
      } else {
        mathContent = mathContent.replace(/(^\$|^\\\()/, '').replace(/(\$$|\\\)$)/, '');
        return `<span class="math-inline">\\( ${mathContent} \\)</span>`;
      }
    });

    // ******** REINSERTAR BLOQUES DE CÓDIGO ********
    text = text.replace(/%%CODE(\d+)%%/g, (match, index) => {
      // Obtener el bloque de código
      const codeBlock = codeBlocks[Number(index)];

      // Añadir un atributo data-needs-escape="true" a los bloques de código con etiquetas HTML
      if (codeBlock.includes('<span') || codeBlock.includes('#569CD6')) {
        return codeBlock.replace('<pre>', '<pre data-needs-escape="true">');
      }

      return codeBlock;
    });

    console.log('Texto después de procesar con formatLaTeX:', text);
    console.log('Bloques de código extraídos:', codeBlocks);
    return text;
  },

  /**
   * Resalta la sintaxis del código según el lenguaje.
   * @param {string} code - Código a resaltar.
   * @param {string} language - Lenguaje de programación.
   * @returns {string} - Código con HTML para resaltado.
   */
  highlightCode(code, language) {
    // SOLUCIÓN RADICAL: Siempre escapar el HTML, sin excepciones
    // Esto garantiza que el código siempre se muestre como texto plano
    return this.escapeHtml(code);
  },

  /**
   * Método antiguo de resaltado de sintaxis (desactivado)
   * @param {string} code - Código a resaltar.
   * @param {string} language - Lenguaje de programación.
   * @returns {string} - Código con HTML para resaltado.
   */
  _highlightCodeOld(code, language) {
    // Syntax rules for different languages
    const syntaxRules = {
      javascript: {
        keywords: /\b(var|let|const|function|return|if|else|for|while|do|switch|case|break|continue|new|try|catch|finally|throw|typeof|instanceof|this|class|extends|super|import|export|from|as|async|await|yield|delete|void|default|null|undefined|in|of|get|set|static)\b/g,
        controlKeywords: /\b(if|else|for|while|do|switch|case|break|continue|return|try|catch|finally|throw)\b/g,
        types: /\b(Array|Object|String|Number|Boolean|Function|Symbol|Map|Set|Promise|Date|RegExp)\b/g,
        constants: /\b(true|false|null|undefined|NaN|Infinity)\b/g,
        functions: /\b([a-zA-Z_$][a-zA-Z0-9_$]*)\s*(?=\()/g,
        classes: /\b([A-Z][a-zA-Z0-9_$]*)\b/g,
        properties: /\.([a-zA-Z_$][a-zA-Z0-9_$]*)\b/g,
        strings: /(["'`])((?:\\\1|(?:(?!\1).))*?)(\1)|`(?:[^`\\]|\\.|\${(?:[^{}]|{[^}]*})*})*`/g,
        templateStrings: /(`(?:[^`\\]|\\.|\${(?:[^{}]|{[^}]*})*})*`)/g,
        // IMPORTANT: Fixed regex for comments to ensure they're captured correctly
        comments: /(\/\/.*?)(?:\n|$)|(\/\*[\s\S]*?\*\/)/g,
        numbers: /\b(0[xX][0-9a-fA-F]+|0[bB][01]+|0[oO][0-7]+|\d+(?:\.\d*)?(?:[eE][+-]?\d+)?)\b/g,
        operators: /([+\-*/%=!|><&]+)/g,
        brackets: /([(){}\[\]])/g,
        decorators: /(@[a-zA-Z_$][a-zA-Z0-9_$]*)/g,
        regexps: /(\/)(?!\/)(?:\\\1|.)*?(?=\1)\1[gimuy]*/g,
      },
      python: {
        keywords: /\b(and|as|assert|async|await|break|class|continue|def|del|elif|else|except|finally|for|from|global|if|import|in|is|lambda|nonlocal|not|or|pass|raise|return|try|while|with|yield)\b/g,
        controlKeywords: /\b(if|elif|else|for|while|break|continue|return|try|except|finally|raise|with)\b/g,
        types: /\b(int|float|str|list|tuple|dict|set|bool|bytes|bytearray|memoryview|complex|range|frozenset|type)\b/g,
        constants: /\b(True|False|None|NotImplemented|Ellipsis|__debug__)\b/g,
        functions: /\b(def\s+)([a-zA-Z_][a-zA-Z0-9_]*)/g,
        functions2: /\b([a-zA-Z_][a-zA-Z0-9_]*)\s*(?=\()/g,
        classes: /\b(class\s+)([a-zA-Z_][a-zA-Z0-9_]*)/g,
        decorators: /(@[a-zA-Z_][a-zA-Z0-9_\.]*)/g,
        strings: /("""[\s\S]*?"""|'''[\s\S]*?'''|"(?:[^"\\]|\\.)*"|'(?:[^'\\]|\\.)*')/g,
        // IMPORTANT: Fixed regex for Python comments
        comments: /(#.*?)(?:\n|$)/g,
        numbers: /\b(0[xX][0-9a-fA-F]+|0[bB][01]+|0[oO][0-7]+|\d+(?:\.\d*)?(?:[eE][+-]?\d+)?[jJ]?)\b/g,
        fStrings: /f(['"])((?:\\\1|(?:(?!\1).))*?)(\1)/g,
        operators: /([+\-*/%=!|><&]+)(?=[,)])/g,
      },
      plaintext: {},
    };

    // Select rules for the specified language or use plaintext as fallback
    const rules = syntaxRules[language] || syntaxRules.plaintext;

    // IMPORTANT CHANGE: Create temporary replacements for comments and strings
    // to prevent other syntax highlighting from breaking them
    const commentPlaceholders = [];
    const stringPlaceholders = [];
    let placeholderIndex = 0;

    // First capture all comments and replace with placeholders
    if (rules.comments) {
      escapedCode = escapedCode.replace(rules.comments, (match) => {
        const placeholder = `__COMMENT_PLACEHOLDER_${placeholderIndex}__`;
        commentPlaceholders[placeholderIndex] = match;
        placeholderIndex++;
        return placeholder;
      });
    }

    // Then capture all strings and replace with placeholders
    if (rules.strings) {
      escapedCode = escapedCode.replace(rules.strings, (match) => {
        const placeholder = `__STRING_PLACEHOLDER_${placeholderIndex}__`;
        stringPlaceholders[placeholderIndex] = match;
        placeholderIndex++;
        return placeholder;
      });
    }

    // Apply other syntax rules
    if (rules.templateStrings) {
      escapedCode = escapedCode.replace(rules.templateStrings, `<span style="color: ${colors.string};">$&</span>`);
    }

    if (rules.fStrings) {
      escapedCode = escapedCode.replace(rules.fStrings, `<span style="color: ${colors.string};">$&</span>`);
    }

    if (rules.heredocs) {
      escapedCode = escapedCode.replace(rules.heredocs, `<span style="color: ${colors.string};">$&</span>`);
    }

    if (rules.regexps) {
      escapedCode = escapedCode.replace(rules.regexps, `<span class="syntax-regex">$&</span>`);
    }

    if (rules.constants) {
      escapedCode = escapedCode.replace(rules.constants, `<span class="syntax-constant">$&</span>`);
    }

    if (rules.variables) {
      escapedCode = escapedCode.replace(rules.variables, `<span class="syntax-variable">$&</span>`);
    }

    if (rules.types) {
      escapedCode = escapedCode.replace(rules.types, `<span class="syntax-type">$&</span>`);
    }

    if (rules.controlKeywords) {
      escapedCode = escapedCode.replace(rules.controlKeywords, `<span class="syntax-keyword syntax-bold">$&</span>`);
    }

    if (rules.keywords) {
      escapedCode = escapedCode.replace(rules.keywords, `<span class="syntax-keyword syntax-bold">$&</span>`);
    }

    if (rules.decorators) {
      escapedCode = escapedCode.replace(rules.decorators, `<span class="syntax-decorator">$&</span>`);
    }

    if (rules.numbers) {
      escapedCode = escapedCode.replace(rules.numbers, `<span class="syntax-number">$&</span>`);
    }

    if (rules.operators) {
      escapedCode = escapedCode.replace(rules.operators, `<span class="syntax-operator">$&</span>`);
    }

    // Apply other language-specific rules...

    // IMPORTANT CHANGE: Now restore comments with proper highlighting
    for (let i = 0; i < commentPlaceholders.length; i++) {
      const placeholder = `__COMMENT_PLACEHOLDER_${i}__`;
      escapedCode = escapedCode.replace(placeholder,
        `<span style="color: ${colors.comment};">${commentPlaceholders[i]}</span>`);
    }

    // Restore strings with proper highlighting
    for (let i = 0; i < stringPlaceholders.length; i++) {
      const placeholder = `__STRING_PLACEHOLDER_${i}__`;
      escapedCode = escapedCode.replace(placeholder,
        `<span style="color: ${colors.string};">${stringPlaceholders[i]}</span>`);
    }

    // Wrap the entire code block in <code> tags
    return `<code class="language-${language}">${escapedCode}</code>`;
  },
};



// Inicializar el chat
ChatBot.initialize();
