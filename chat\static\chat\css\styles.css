/* Import variables from chat app */
@import url('/static/chat/css/variables.css');

/* Import base styles */
@import url('/static/chat/css/base.css');

/* Additional base styles */
body {
    min-height: 100vh;
    height: auto;
    display: block;
}

/* Navigation */
.navbar {
    padding: 1.5rem 0;
    background-color: var(--bg-secondary);
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    text-decoration: none;
    letter-spacing: 0.5px;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.nav-btn {
    padding: 0.5rem 1.5rem;
    text-decoration: none;
    color: var(--text-primary);
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
}

.nav-btn.primary {
    background-color: var(--accent-primary);
    color: var(--text-primary);
}

.nav-btn:hover {
    background-color: var(--accent-secondary);
    transform: translateY(-2px);
}

.welcome-user {
    color: var(--text-dimmed);
    font-size: var(--font-size-sm);
}

/* Auth Section */
.auth-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;
    background: linear-gradient(135deg, var(--bg-secondary), var(--bg-primary));
    position: relative;
    overflow: hidden;
}

.auth-container {
    background-color: var(--bg-tertiary);
    border-radius: var(--border-radius-lg);
    padding: 2.5rem;
    width: 100%;
    max-width: 450px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 2;
}

.auth-container h2 {
    color: var(--text-primary);
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    text-align: center;
}

.auth-container form {
    display: flex;
    flex-direction: column;
}

.auth-container label {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    font-size: var(--font-size-sm);
}

.auth-container input {
    padding: 0.8rem;
    margin-bottom: 1rem;
    background-color: var(--bg-quaternary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: var(--font-size);
    transition: var(--transition);
}

.auth-container input:focus {
    border-color: var(--accent-primary);
    outline: none;
    box-shadow: 0 0 0 2px rgba(var(--accent-primary-rgb), 0.2);
}

.auth-button {
    width: 100%;
    padding: 0.9rem;
    background-color: var(--accent-primary);
    color: var(--text-primary);
    border: none;
    border-radius: var(--border-radius);
    font-size: var(--font-size);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    margin-top: 1rem;
}

.auth-button:hover {
    background-color: var(--accent-secondary);
    transform: translateY(-2px);
}

.auth-link {
    text-align: center;
    margin-top: 1.5rem;
    color: var(--text-dimmed);
    font-size: var(--font-size-sm);
}

.auth-link a {
    color: var(--accent-primary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.auth-link a:hover {
    color: var(--accent-secondary);
}

/* Register Container */
#register-container {
    max-width: 700px;
}

#register-container form {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

#register-container button {
    grid-column: span 2;
}

/* Footer */
.footer {
    padding: 2rem 0;
    background-color: var(--bg-secondary);
    color: var(--text-dimmed);
    text-align: center;
    font-size: var(--font-size-sm);
    border-top: 1px solid var(--border-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar .container {
        flex-direction: column;
        gap: 1rem;
    }

    .auth-container {
        padding: 2rem;
        margin: 0 15px;
    }

    #register-container form {
        grid-template-columns: 1fr;
    }

    #register-container button {
        grid-column: auto;
    }
}
