/* base.css - Base styles for the application */

/* Import fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Nunito:ital,wght@0,200..1000;1,200..1000&family=Literata:ital,opsz,wght@0,7..72,300;0,7..72,400;0,7..72,500;0,7..72,600;1,7..72,400;1,7..72,500&display=swap');

/* Reset and base styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-family: var(--font-family-primary);
}

html {
    height: 100%;
    width: 100%;
    padding: 0;
    margin: 0;
    overflow-x: hidden;
    overflow-y: scroll; /* Enable global scroll */
    scroll-behavior: smooth;
}

body {
    min-height: 100vh;
    width: 100%;
    padding: 0;
    margin: 0;
    color: var(--text-primary);
    overflow-x: hidden;
    overflow-y: visible; /* Allow content overflow for global scroll */
    display: block; /* Block layout for better scroll behavior */
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

h1 {
    font-size: var(--font-size-xl);
}

h2 {
    font-size: var(--font-size-lg);
}

h3 {
    font-size: calc(var(--font-size-lg) * 0.9);
}

h4 {
    font-size: var(--font-size);
}

h5 {
    font-size: var(--font-size);
}

h6 {
    font-size: var(--font-size-sm);
}

p {
    margin-bottom: var(--spacing-md);
}

a {
    color: var(--accent-secondary);
    text-decoration: none;
}

a:hover {
    color: var(--accent-primary);
}

/* Buttons */
button {
    cursor: pointer;
    border: none;
    background: none;
    font-family: var(--font-family-primary);
}

button:focus {
    outline: none;
}

/* Inputs */
input, textarea, select {
    font-family: inherit;
    font-size: inherit;
    color: inherit;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-sm) var(--spacing-md);
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: var(--accent-primary);
}

/* Textarea */
textarea {
    resize: vertical;
    min-height: 40px;
}

/* Lists */
ul, ol {
    list-style-position: inside;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background-color: var(--text-dimmed);
    border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb:hover {
    background-color: var(--accent-primary);
}

/* Code blocks */
pre, code {
    font-family: var(--font-family-code);
    border-radius: var(--border-radius-sm);
}

pre {
    padding: var(--spacing-md);
    overflow-x: auto;
    background-color: var(--bg-tertiary);
    margin: var(--spacing-md) 0;
}

code {
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

pre code {
    background-color: transparent;
    padding: 0;
    border-radius: 0;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin: var(--spacing-md) 0;
}

th, td {
    padding: var(--spacing-sm) var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    font-weight: 600;
    background-color: rgba(var(--bg-tertiary-rgb), 0.5);
}

/* Images */
img {
    max-width: 100%;
    height: auto;
}
