/* chat.css - Styles for the chat container and overall structure */

/*
 * Note: This file contains chat-specific styles that complement layout.css
 * Avoid duplicating styles that are already defined in layout.css
 */

/* Chat container - specific background styling */
.chat-container {
  background: linear-gradient(135deg, var(--chat-bg-secondary) 0%, var(--chat-bg-primary) 100%);
}

/* Chat-specific responsive adjustments */
@media (max-width: 1200px) {
  .chat-box {
    max-width: 60%;
    width: 60%;
  }
}

@media (max-width: 992px) {
  .chat-box {
    max-width: 85%;
    width: 85%;
  }
}

@media (max-width: 768px) {
  .chat-box {
    max-width: 90%;
    width: 90%;
  }
}

@media (max-width: 480px) {
  .chat-box {
    max-width: 95%;
    width: 95%;
  }
}