* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #6366f1;
    --primary-hover: #5855eb;
    --text-dark: #1f2937;
    --text-gray: #6b7280;
    --text-light: #9ca3af;
    --bg-gray: #f9fafb;
    --border-color: #e5e7eb;
    --white: #ffffff;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    overflow-x: hidden;
}

/* Header */
header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: all 0.3s ease;
}

nav {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-dark);
    text-decoration: none;
}

.logo::before {
    content: '◉';
    color: var(--primary-color);
    margin-right: 0.5rem;
    font-size: 1.2rem;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-gray);
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-links a:hover {
    color: var(--primary-color);
}

.nav-cta {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.welcome-user {
    color: var(--text-gray);
    font-weight: 500;
    margin-right: 0.5rem;
}

.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
}

.btn-ghost {
    background: transparent;
    color: var(--text-gray);
    border: 1px solid var(--border-color);
}

.btn-ghost:hover {
    background: var(--bg-gray);
    color: var(--text-dark);
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 4px 14px 0 rgba(99, 102, 241, 0.3);
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(99, 102, 241, 0.4);
}

/* Hero Section */
.hero {
    padding: 8rem 2rem 4rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 200%;
    background: radial-gradient(circle, rgba(99, 102, 241, 0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(2deg); }
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.hero h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.1;
    background: linear-gradient(135deg, var(--text-dark) 0%, var(--primary-color) 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.hero p {
    font-size: 1.25rem;
    color: var(--text-gray);
    margin-bottom: 2.5rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-cta {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 3rem;
}

.hero-visual {
    margin-top: 2rem;
    position: relative;
}

.mockup {
    background: white;
    border-radius: 16px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
    padding: 2rem;
    max-width: 600px;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
}

.mockup::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), #8b5cf6, #ec4899);
}

.chat-interface {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.model-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: var(--bg-gray);
    border-radius: 8px;
    font-size: 0.9rem;
    color: var(--text-gray);
}

.message {
    padding: 1rem;
    border-radius: 12px;
    max-width: 80%;
}

.message.user {
    background: var(--primary-color);
    color: white;
    align-self: flex-end;
}

.message.ai {
    background: var(--bg-gray);
    color: var(--text-dark);
    align-self: flex-start;
}

/* Features Section */
.features {
    padding: 6rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.feature-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 4rem;
}

.feature {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s ease forwards;
}

.feature:nth-child(even) {
    direction: rtl;
}

.feature:nth-child(even) > * {
    direction: ltr;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.feature-content h3 {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.feature-content p {
    font-size: 1.1rem;
    color: var(--text-gray);
    line-height: 1.7;
}

.feature-visual {
    background: linear-gradient(135deg, var(--bg-gray) 0%, #f3f4f6 100%);
    border-radius: 16px;
    padding: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 250px;
    position: relative;
    overflow: hidden;
}

.feature-visual::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(99, 102, 241, 0.1), transparent);
    animation: rotate 10s linear infinite;
}

@keyframes rotate {
    to { transform: rotate(360deg); }
}

.feature-icon {
    font-size: 4rem;
    position: relative;
    z-index: 2;
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #8b5cf6 100%);
    color: white;
    padding: 6rem 2rem;
    text-align: center;
}

.cta-content {
    max-width: 600px;
    margin: 0 auto;
}

.cta-section h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.cta-section p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.btn-white {
    background: white;
    color: var(--primary-color);
    font-weight: 600;
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.btn-white:hover {
    background: #f9fafb;
    transform: translateY(-2px);
}

/* Testimonials */
.testimonials {
    padding: 6rem 2rem;
    background: var(--bg-gray);
}

.testimonials-container {
    max-width: 1000px;
    margin: 0 auto;
    text-align: center;
}

.testimonials h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    color: var(--text-dark);
}

.testimonial-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.testimonial {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.testimonial:hover {
    transform: translateY(-5px);
}

.testimonial p {
    font-style: italic;
    margin-bottom: 1.5rem;
    color: var(--text-gray);
}

.testimonial-author {
    font-weight: 600;
    color: var(--text-dark);
}

/* Footer */
footer {
    background: var(--text-dark);
    color: white;
    padding: 3rem 2rem 1rem;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.footer-section h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #9ca3af;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: white;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    margin-top: 2rem;
    border-top: 1px solid #374151;
    color: #9ca3af;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero p {
        font-size: 1.1rem;
    }

    .hero-cta {
        flex-direction: column;
        align-items: center;
    }

    .feature {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .feature:nth-child(even) {
        direction: ltr;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .cta-section h2 {
        font-size: 2rem;
    }
}

/* Scroll animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* Authentication Pages */
.auth-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6rem 2rem 3rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    position: relative;
}

.auth-section::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 200%;
    background: radial-gradient(circle, rgba(99, 102, 241, 0.05) 0%, transparent 70%);
    animation: float 8s ease-in-out infinite;
}

.auth-container {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
    width: 100%;
    max-width: 400px;
    position: relative;
    z-index: 2;
    max-height: calc(100vh - 10rem);
    overflow-y: auto;
}

.auth-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), #8b5cf6, #ec4899);
    border-radius: 16px 16px 0 0;
}

.auth-container h2 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-dark);
    text-align: center;
    margin-bottom: 1.5rem;
}

.auth-container form {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
}

.auth-container p {
    margin-bottom: 0.75rem;
}

.auth-container label {
    font-weight: 500;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    display: block;
}

.auth-container input[type="text"],
.auth-container input[type="password"],
.auth-container input[type="email"] {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Inter', sans-serif;
    transition: all 0.3s ease;
    background: white;
    color: var(--text-dark);
}

.auth-container input[type="text"]:focus,
.auth-container input[type="password"]:focus,
.auth-container input[type="email"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.auth-container .errorlist {
    list-style: none;
    padding: 0;
    margin: 0.5rem 0 0 0;
}

.auth-container .errorlist li {
    color: #dc2626;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.auth-container .helptext {
    font-size: 0.875rem;
    color: var(--text-gray);
    margin-top: 0.25rem;
}

.auth-button {
    width: 100%;
    padding: 0.875rem 1.5rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    font-family: 'Inter', sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 14px 0 rgba(99, 102, 241, 0.3);
    margin-top: 1rem;
}

.auth-button:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(99, 102, 241, 0.4);
}

.auth-link {
    text-align: center;
    margin-top: 1.5rem;
    color: var(--text-gray);
    font-size: 0.9rem;
}

.auth-link a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.auth-link a:hover {
    color: var(--primary-hover);
}

/* Django form styling adjustments */
.auth-container form p {
    margin-bottom: 0;
}

.auth-container form p:last-of-type {
    margin-bottom: 1rem;
}

/* Alert messages */
.alert {
    padding: 0.875rem 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
    font-weight: 500;
}

.alert-error {
    background-color: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.alert-success {
    background-color: #f0fdf4;
    color: #16a34a;
    border: 1px solid #bbf7d0;
}

.alert-info {
    background-color: #eff6ff;
    color: #2563eb;
    border: 1px solid #bfdbfe;
}

.alert-warning {
    background-color: #fffbeb;
    color: #d97706;
    border: 1px solid #fed7aa;
}

/* Registration layout with help text */
.registration-layout {
    display: grid;
    grid-template-columns: 2fr 3fr;
    gap: 2rem;
    max-width: 1100px;
    width: 100%;
    align-items: start;
    min-height: 0;
}

.help-text-container {
    background: linear-gradient(135deg, var(--bg-gray) 0%, #f3f4f6 100%);
    padding: 1.5rem;
    border-radius: 16px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    max-height: calc(100vh - 12rem);
    overflow-y: auto;
}

.help-text-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #10b981, #059669, #047857);
    border-radius: 16px 16px 0 0;
}

.help-text-container h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.help-text-container h3::before {
    content: '💡';
    font-size: 1rem;
}

.help-section {
    margin-bottom: 1.25rem;
}

.help-section:last-child {
    margin-bottom: 0;
}

.help-section h4 {
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.help-section h4::before {
    content: '▸';
    color: var(--primary-color);
    font-weight: bold;
}

.help-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.help-section li {
    padding: 0.35rem 0;
    color: var(--text-gray);
    font-size: 0.85rem;
    line-height: 1.4;
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
}

.help-section li::before {
    content: '✓';
    color: #10b981;
    font-weight: bold;
    margin-top: 0.1rem;
    flex-shrink: 0;
}

.help-section .requirement {
    background: white;
    padding: 0.6rem;
    border-radius: 8px;
    margin-bottom: 0.4rem;
    border-left: 3px solid var(--primary-color);
}

.help-section .requirement:last-child {
    margin-bottom: 0;
}

.help-section .requirement strong {
    color: var(--text-dark);
    font-size: 0.85rem;
}

/* Mobile responsiveness for auth pages */
@media (max-width: 768px) {
    .auth-section {
        padding: 5rem 1rem 2rem;
    }

    .registration-layout {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        max-width: 100%;
    }

    .auth-container {
        padding: 1.5rem;
        margin: 0;
        max-height: none;
    }

    .auth-container h2 {
        font-size: 1.5rem;
        margin-bottom: 1.25rem;
    }

    .help-text-container {
        order: 2;
        padding: 1.25rem;
        max-height: none;
        overflow-y: visible;
    }

    .help-text-container h3 {
        font-size: 1rem;
        margin-bottom: 1rem;
    }

    .help-section {
        margin-bottom: 1rem;
    }

    .help-section h4 {
        font-size: 0.9rem;
        margin-bottom: 0.4rem;
    }

    .help-section li {
        padding: 0.3rem 0;
        font-size: 0.8rem;
    }
}
