/* segment-chat.css - Styles for segment-specific chat functionality */

/* Text selection styling */
.message-content {
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}

.message-content::selection {
  background-color: rgba(var(--accent-primary-rgb), 0.2);
  color: var(--text-primary);
}

.message-content::-moz-selection {
  background-color: rgba(var(--accent-primary-rgb), 0.2);
  color: var(--text-primary);
}

/* Selected text highlight */
.segment-selected-text {
  background-color: rgba(var(--accent-primary-rgb), 0.1);
  border-radius: var(--border-radius-sm);
  padding: 2px 4px;
  border: 1px solid rgba(var(--accent-primary-rgb), 0.3);
}

/* Context menu for text selection */
.segment-context-menu {
  position: absolute;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  padding: var(--spacing-xs);
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition-theme);
  min-width: 160px;
}

.segment-context-menu.active {
  opacity: 1;
  visibility: visible;
}

.segment-context-menu button {
  background: var(--accent-primary);
  border: none;
  color: white;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  font-size: var(--font-size-sm);
  font-weight: 500;
  transition: var(--transition-theme);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  white-space: nowrap;
  width: 100%;
  justify-content: center;
}

.segment-context-menu button:hover {
  background: var(--accent-secondary);
}

.segment-context-menu button i {
  font-size: 1rem;
}

/* Segment chat modal */
.segment-chat-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition-theme);
  background-color: var(--modal-backdrop);
}

.light-theme .segment-chat-modal {
  background-color: var(--modal-backdrop-light);
}

.segment-chat-modal.active {
  opacity: 1;
  visibility: visible;
}

.segment-chat-content {
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  width: 90%;
  max-width: 700px;
  height: 70vh;
  max-height: 500px;
  display: flex;
  flex-direction: column;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

/* Segment chat header */
.segment-chat-header {
  background: var(--bg-tertiary);
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.segment-chat-title {
  color: var(--text-primary);
  font-weight: 600;
  font-size: var(--font-size-lg);
  margin: 0;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.segment-chat-close {
  background: transparent;
  border: none;
  color: var(--text-dimmed);
  font-size: 1.2rem;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  transition: var(--transition-theme);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
}

.segment-chat-close:hover {
  background: var(--bg-quaternary);
  color: var(--text-primary);
}

/* Segment context display */
.segment-context {
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-md);
  flex-shrink: 0;
}

.segment-context-label {
  color: var(--text-dimmed);
  font-size: var(--font-size-sm);
  font-weight: 500;
  margin-bottom: var(--spacing-xs);
}

.segment-context-text {
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  line-height: var(--line-height);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-sm);
  font-style: italic;
  max-height: 80px;
  overflow-y: auto;
}

/* Segment chat messages area */
.segment-chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.segment-chat-messages::-webkit-scrollbar {
  width: 6px;
}

.segment-chat-messages::-webkit-scrollbar-track {
  background: transparent;
}

.segment-chat-messages::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: var(--border-radius-sm);
}

.segment-chat-messages::-webkit-scrollbar-thumb:hover {
  background: var(--text-dimmed);
}

/* Segment message styling */
.segment-message {
  display: flex;
  flex-direction: column;
  max-width: 80%;
}

.segment-message.user {
  align-self: flex-end;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius) var(--border-radius-sm) var(--border-radius) var(--border-radius);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-primary);
}

.segment-message.bot {
  align-self: flex-start;
  background: var(--bg-quaternary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm) var(--border-radius) var(--border-radius) var(--border-radius);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-primary);
}

.segment-message-content {
  line-height: var(--line-height);
  word-wrap: break-word;
}

/* Segment chat input */
.segment-chat-input {
  background: var(--bg-tertiary);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-md);
  flex-shrink: 0;
}

.segment-input-container {
  display: flex;
  gap: var(--spacing-sm);
  align-items: flex-end;
}

.segment-input-field {
  flex: 1;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-primary);
  font-size: var(--font-size);
  line-height: var(--line-height);
  resize: none;
  min-height: 40px;
  max-height: 120px;
  transition: var(--transition-theme);
}

.segment-input-field:focus {
  outline: none;
  border-color: var(--accent-primary);
}

.segment-send-btn {
  background: var(--accent-primary);
  border: none;
  border-radius: var(--border-radius);
  color: white;
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  font-weight: 500;
  transition: var(--transition-theme);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 60px;
  height: 40px;
}

.segment-send-btn:hover:not(:disabled) {
  background: var(--accent-secondary);
}

.segment-send-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Loading state */
.segment-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md);
  color: var(--text-dimmed);
  font-size: var(--font-size-sm);
}

.segment-loading::after {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: var(--spacing-sm);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .segment-chat-content {
    width: 95%;
    height: 85vh;
    max-height: none;
  }

  .segment-context-text {
    max-height: 60px;
  }

  .segment-message {
    max-width: 90%;
  }
}

@media (max-width: 480px) {
  .segment-chat-header,
  .segment-context,
  .segment-chat-messages,
  .segment-chat-input {
    padding: var(--spacing-sm);
  }
}
