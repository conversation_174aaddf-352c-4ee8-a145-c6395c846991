/**
 * <PERSON><PERSON><PERSON><PERSON> chat-nav.js - Navegación del chat
 *
 * Este módulo proporciona funcionalidad para navegar rápidamente
 * entre el inicio y el final del chat.
 */

/**
 * Inicializa los botones de navegación del chat
 */
export function initializeChatNavigation() {
  const goToTopBtn = document.getElementById('go-to-top-btn');
  const goToBottomBtn = document.getElementById('go-to-bottom-btn');
  const chatNavButtons = document.querySelector('.chat-nav-buttons');

  if (!goToTopBtn || !goToBottomBtn || !chatNavButtons) return;

  // Configurar evento para ir al inicio del chat
  goToTopBtn.addEventListener('click', () => {
    scrollToFirstMessage();
  });

  // Configurar evento para ir al final del chat
  goToBottomBtn.addEventListener('click', () => {
    scrollToLastMessage();
  });

  // Observar cambios en el DOM para mostrar/ocultar los botones
  const observer = new MutationObserver(() => {
    updateButtonsVisibility(chatNavButtons);
  });

  // Iniciar observación en el contenedor de mensajes
  const chatBoxContent = document.getElementById('chat-box-content');
  if (chatBoxContent) {
    observer.observe(chatBoxContent, {
      childList: true,
      subtree: true
    });
  }

  // Verificar inicialmente si hay mensajes
  updateButtonsVisibility(chatNavButtons);
}

/**
 * Hace scroll al inicio de la página
 */
function scrollToFirstMessage() {
  // Ir al inicio de la página
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  });
}

/**
 * Hace scroll al final de la página
 */
function scrollToLastMessage() {
  // Ir al final de la página
  window.scrollTo({
    top: document.body.scrollHeight,
    behavior: 'smooth'
  });
}

/**
 * Actualiza la visibilidad de los botones de navegación
 * @param {HTMLElement} buttonsContainer - Contenedor de los botones
 */
function updateButtonsVisibility(buttonsContainer) {
  const messages = document.querySelectorAll('.chat-message');

  // Mostrar los botones solo si hay mensajes
  if (messages.length > 0) {
    buttonsContainer.classList.add('visible');
  } else {
    buttonsContainer.classList.remove('visible');
  }
}
