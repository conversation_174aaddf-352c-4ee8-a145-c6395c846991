{% load static %}
<!DOCTYPE html>
<html lang="es">
<head>
    <title>Test Inline Code Styling</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Main CSS file that imports all other CSS files -->
    <link rel="stylesheet" href="{% static 'chat/css/main.css' %}">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Fira+Code:wght@400;500&display=swap">
    <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
</head>
<body class="light-theme">
    <div style="padding: 2rem; max-width: 800px; margin: 0 auto;">
        <h1>Inline Code Styling Test</h1>
        
        <div class="message-content">
            <h2>Testing Inline Code in Light Theme</h2>
            <p>Here are some examples of inline code segments that should have improved contrast:</p>
            
            <p>This is a paragraph with <code>handle_input()</code>, <code>update()</code>, and <code>draw()</code> inline code segments.</p>
            
            <p>More examples: <code>var myVariable = "hello"</code> and <code>function processData()</code> and <code>console.log()</code>.</p>
            
            <p>Python examples: <code>def my_function():</code> and <code>import numpy as np</code> and <code>print("Hello World")</code>.</p>
            
            <h3>Mixed Content</h3>
            <p>Regular text with <code>inline code</code> and more regular text. The background should be visible but not too dark.</p>
            
            <ul>
                <li>List item with <code>some_code()</code></li>
                <li>Another item with <code>another_function</code></li>
                <li>Third item with <code>variable_name</code></li>
            </ul>
        </div>
        
        <div style="margin-top: 2rem; padding: 1rem; border: 1px solid var(--border-color); border-radius: var(--border-radius);">
            <h3>Theme Toggle Test</h3>
            <button onclick="toggleTheme()" style="padding: 0.5rem 1rem; margin-bottom: 1rem;">Toggle Dark/Light Theme</button>
            <p>Click the button above to test inline code in both themes: <code>theme.toggle()</code></p>
        </div>
    </div>

    <script>
        function toggleTheme() {
            const body = document.body;
            if (body.classList.contains('light-theme')) {
                body.classList.remove('light-theme');
                body.classList.add('dark-theme');
            } else {
                body.classList.remove('dark-theme');
                body.classList.add('light-theme');
            }
        }
    </script>
</body>
</html>
